import { PrismaService } from '../prisma/prisma.service';
import { CreateExerciseLogDto } from './dto/create-exercise-log.dto';
import { UpdateExerciseLogDto } from './dto/update-exercise-log.dto';
import { QuickLogExerciseDto } from './dto/quick-log-exercise.dto';
import { DailyExerciseStatsDto, WeeklyExerciseStatsDto, MonthlyExerciseStatsDto, WorkoutPlanProgressDto, ExercisePerformanceDto } from './dto/exercise-statistics.dto';
export declare class ExerciseLogsService {
    private prisma;
    constructor(prisma: PrismaService);
    createExerciseLog(createExerciseLogDto: CreateExerciseLogDto): Promise<{
        setsLog: {
            id: string;
            weight: number | null;
            setNumber: number | null;
            reps: number | null;
            times: number | null;
            caloriesBurned: number | null;
            exerciseLogId: string;
        }[];
    } & {
        id: string;
        workoutLogId: string;
    }>;
    findExerciseLogById(id: string): Promise<{
        setsLog: {
            id: string;
            weight: number | null;
            setNumber: number | null;
            reps: number | null;
            times: number | null;
            caloriesBurned: number | null;
            exerciseLogId: string;
        }[];
    } & {
        id: string;
        workoutLogId: string;
    }>;
    findUserExerciseLogs(userId: string, startDate?: string, endDate?: string): Promise<({
        workoutExerciseLogs: ({
            workoutExercise: {
                workoutPlan: {
                    name: string | null;
                    id: string;
                    planType: import(".prisma/client").$Enums.PlanType | null;
                };
            } & {
                id: string;
                weight: number | null;
                workoutPlanId: string;
                exerciseId: string | null;
                dayNumber: number | null;
            };
        } & {
            id: string;
            dayNumber: number | null;
            caloriesBurned: number | null;
            date: Date | null;
            progressPercent: number | null;
            logId: string;
            workoutExerciseId: string;
        })[];
    } & {
        id: string;
        weight: number | null;
        height: number | null;
        userId: string;
        caloriesBurned: number | null;
        notes: string | null;
        dateLogged: Date | null;
        caloriesIntake: number | null;
        mealId: string | null;
    })[]>;
    updateExerciseLog(id: string, updateExerciseLogDto: UpdateExerciseLogDto): Promise<{
        setsLog: {
            id: string;
            weight: number | null;
            setNumber: number | null;
            reps: number | null;
            times: number | null;
            caloriesBurned: number | null;
            exerciseLogId: string;
        }[];
    } & {
        id: string;
        workoutLogId: string;
    }>;
    deleteExerciseLog(id: string): Promise<{
        id: string;
        workoutLogId: string;
    }>;
    getDailyStats(userId: string, date: string): Promise<DailyExerciseStatsDto>;
    getWeeklyStats(userId: string, weekStart: string): Promise<WeeklyExerciseStatsDto>;
    getMonthlyStats(userId: string, month: string): Promise<MonthlyExerciseStatsDto>;
    getWorkoutPlanProgress(userId: string, workoutPlanId: string): Promise<WorkoutPlanProgressDto>;
    getExercisePerformance(userId: string, exerciseId?: string): Promise<ExercisePerformanceDto[]>;
    getUserWorkoutPlansProgress(userId: string): Promise<WorkoutPlanProgressDto[]>;
    getExerciseStreaks(userId: string): Promise<{
        currentStreak: number;
        longestStreak: number;
        lastWorkoutDate: string | null;
    }>;
    quickLogExercise(userId: string, quickLogDto: QuickLogExerciseDto): Promise<{
        setsLog: {
            id: string;
            weight: number | null;
            setNumber: number | null;
            reps: number | null;
            times: number | null;
            caloriesBurned: number | null;
            exerciseLogId: string;
        }[];
    } & {
        id: string;
        workoutLogId: string;
    }>;
}
