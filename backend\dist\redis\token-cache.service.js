"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var TokenCacheService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TokenCacheService = void 0;
const cache_manager_1 = require("@nestjs/cache-manager");
const common_1 = require("@nestjs/common");
let TokenCacheService = TokenCacheService_1 = class TokenCacheService {
    cacheManager;
    logger = new common_1.Logger(TokenCacheService_1.name);
    constructor(cacheManager) {
        this.cacheManager = cacheManager;
    }
    async storeRefreshToken(userId, refreshToken, ttl = 604800) {
        const key = `refresh_token:${refreshToken}`;
        this.logger.debug(`Attempting to store refresh token in cache...`);
        this.logger.debug(`Key: ${key}`);
        this.logger.debug(`UserId: ${userId}`);
        this.logger.debug(`TTL: ${ttl} seconds`);
        try {
            const ttlMs = ttl * 1000;
            this.logger.debug(`TTL in milliseconds: ${ttlMs}`);
            await this.cacheManager.set(key, userId, ttlMs);
            this.logger.debug(`Successfully called cacheManager.set()`);
            const stored = await this.cacheManager.get(key);
            this.logger.debug(`Cache verification - Key exists: ${stored !== null && stored !== undefined}`);
            this.logger.debug(`Cache verification - Stored value: ${stored}`);
            this.logger.debug(`Cache verification - Type: ${typeof stored}`);
            try {
                const cacheStore = this.cacheManager.store;
                if (cacheStore && cacheStore.client) {
                    const redisClient = cacheStore.client;
                    this.logger.debug(`Redis client available: ${!!redisClient}`);
                    const directGet = await redisClient.get(key);
                    this.logger.debug(`Direct Redis get result: ${directGet}`);
                }
                else {
                    this.logger.debug(`Cache store or client not available`);
                }
            }
            catch (redisError) {
                this.logger.debug(`Direct Redis access failed: ${redisError.message}`);
            }
            if (stored === null || stored === undefined) {
                throw new Error('Token was not stored in cache');
            }
        }
        catch (error) {
            this.logger.error(`Failed to store refresh token: ${error.message}`);
            throw error;
        }
    }
    async getUserIdByRefreshToken(refreshToken) {
        const key = `refresh_token:${refreshToken}`;
        try {
            const result = await this.cacheManager.get(key);
            this.logger.debug(`Retrieved value for ${key}: ${result}`);
            return result || null;
        }
        catch (error) {
            this.logger.error(`Failed to get refresh token: ${error.message}`);
            throw error;
        }
    }
    async removeRefreshToken(refreshToken) {
        const key = `refresh_token:${refreshToken}`;
        try {
            await this.cacheManager.del(key);
            this.logger.debug(`Removed refresh token: ${key}`);
        }
        catch (error) {
            this.logger.error(`Failed to remove refresh token: ${error.message}`);
            throw error;
        }
    }
    async removeAllUserRefreshTokens(userId) {
        this.logger.warn('removeAllUserRefreshTokens not implemented yet');
    }
};
exports.TokenCacheService = TokenCacheService;
exports.TokenCacheService = TokenCacheService = TokenCacheService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(cache_manager_1.CACHE_MANAGER)),
    __metadata("design:paramtypes", [Object])
], TokenCacheService);
//# sourceMappingURL=token-cache.service.js.map