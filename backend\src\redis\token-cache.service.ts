import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { Cache } from 'cache-manager';

@Injectable()
export class TokenCacheService {
  private readonly logger = new Logger(TokenCacheService.name);

  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) {}

  async storeRefreshToken(userId: string, refreshToken: string, ttl: number = 604800): Promise<void> {
    const key = `refresh_token:${refreshToken}`;
    this.logger.debug(`Attempting to store refresh token in cache...`);
    this.logger.debug(`Key: ${key}`);
    this.logger.debug(`UserId: ${userId}`);
    this.logger.debug(`TTL: ${ttl} seconds`);

    try {
      // Convert TTL from seconds to milliseconds for cache-manager
      const ttlMs = ttl * 1000;
      await this.cacheManager.set(key, userId, ttlMs);

      const stored = await this.cacheManager.get(key);
      this.logger.debug(`Cache verification - Key exists: ${stored !== null && stored !== undefined}`);
      this.logger.debug(`Cache verification - Stored value: ${stored}`);

      if (stored === null || stored === undefined) {
        throw new Error('Token was not stored in cache');
      }
    } catch (error) {
      this.logger.error(`Failed to store refresh token: ${error.message}`);
      throw error;
    }
  }

  async getUserIdByRefreshToken(refreshToken: string): Promise<string | null> {
    const key = `refresh_token:${refreshToken}`;
    try {
      const result = await this.cacheManager.get<string>(key);
      this.logger.debug(`Retrieved value for ${key}: ${result}`);
      return result || null;
    } catch (error) {
      this.logger.error(`Failed to get refresh token: ${error.message}`);
      throw error;
    }
  }

  async removeRefreshToken(refreshToken: string): Promise<void> {
    const key = `refresh_token:${refreshToken}`;
    try {
      await this.cacheManager.del(key);
      this.logger.debug(`Removed refresh token: ${key}`);
    } catch (error) {
      this.logger.error(`Failed to remove refresh token: ${error.message}`);
      throw error;
    }
  }

  async removeAllUserRefreshTokens(userId: string): Promise<void> {
    // This would require implementing a reverse lookup or pattern search in Redis
    // For now, we'll just handle single token removal
    this.logger.warn('removeAllUserRefreshTokens not implemented yet');
  }
}