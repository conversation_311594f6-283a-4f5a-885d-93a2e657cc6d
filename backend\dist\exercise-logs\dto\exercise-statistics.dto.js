"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExercisePerformanceDto = exports.WorkoutPlanProgressDto = exports.MonthlyExerciseStatsDto = exports.WeeklyExerciseStatsDto = exports.DailyExerciseStatsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class DailyExerciseStatsDto {
    date;
    totalExercises;
    totalSets;
    totalReps;
    totalCaloriesBurned;
    totalWorkoutTime;
    averageWeight;
    workoutPlansCompleted;
}
exports.DailyExerciseStatsDto = DailyExerciseStatsDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Date of the statistics', example: '2024-01-15' }),
    __metadata("design:type", String)
], DailyExerciseStatsDto.prototype, "date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total exercises performed', example: 5 }),
    __metadata("design:type", Number)
], DailyExerciseStatsDto.prototype, "totalExercises", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total sets performed', example: 25 }),
    __metadata("design:type", Number)
], DailyExerciseStatsDto.prototype, "totalSets", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total repetitions performed', example: 300 }),
    __metadata("design:type", Number)
], DailyExerciseStatsDto.prototype, "totalReps", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total calories burned', example: 450.5 }),
    __metadata("design:type", Number)
], DailyExerciseStatsDto.prototype, "totalCaloriesBurned", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total workout time in minutes', example: 90 }),
    __metadata("design:type", Number)
], DailyExerciseStatsDto.prototype, "totalWorkoutTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Average weight used across exercises', example: 45.5 }),
    __metadata("design:type", Number)
], DailyExerciseStatsDto.prototype, "averageWeight", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Workout plans completed on this day', example: ['Plan A', 'Plan B'] }),
    __metadata("design:type", Array)
], DailyExerciseStatsDto.prototype, "workoutPlansCompleted", void 0);
class WeeklyExerciseStatsDto {
    weekStart;
    weekEnd;
    totalWorkoutDays;
    totalExercises;
    totalCaloriesBurned;
    averageDailyWorkoutTime;
    dailyStats;
}
exports.WeeklyExerciseStatsDto = WeeklyExerciseStatsDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Week start date', example: '2024-01-15' }),
    __metadata("design:type", String)
], WeeklyExerciseStatsDto.prototype, "weekStart", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Week end date', example: '2024-01-21' }),
    __metadata("design:type", String)
], WeeklyExerciseStatsDto.prototype, "weekEnd", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total workout days in the week', example: 5 }),
    __metadata("design:type", Number)
], WeeklyExerciseStatsDto.prototype, "totalWorkoutDays", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total exercises performed in the week', example: 35 }),
    __metadata("design:type", Number)
], WeeklyExerciseStatsDto.prototype, "totalExercises", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total calories burned in the week', example: 2250.5 }),
    __metadata("design:type", Number)
], WeeklyExerciseStatsDto.prototype, "totalCaloriesBurned", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Average daily workout time in minutes', example: 75 }),
    __metadata("design:type", Number)
], WeeklyExerciseStatsDto.prototype, "averageDailyWorkoutTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Daily statistics breakdown', type: [DailyExerciseStatsDto] }),
    __metadata("design:type", Array)
], WeeklyExerciseStatsDto.prototype, "dailyStats", void 0);
class MonthlyExerciseStatsDto {
    month;
    totalWorkoutDays;
    totalExercises;
    totalCaloriesBurned;
    averageWeeklyFrequency;
    mostPerformedExercises;
    weeklyStats;
}
exports.MonthlyExerciseStatsDto = MonthlyExerciseStatsDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Month and year', example: '2024-01' }),
    __metadata("design:type", String)
], MonthlyExerciseStatsDto.prototype, "month", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total workout days in the month', example: 20 }),
    __metadata("design:type", Number)
], MonthlyExerciseStatsDto.prototype, "totalWorkoutDays", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total exercises performed in the month', example: 150 }),
    __metadata("design:type", Number)
], MonthlyExerciseStatsDto.prototype, "totalExercises", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total calories burned in the month', example: 9500.5 }),
    __metadata("design:type", Number)
], MonthlyExerciseStatsDto.prototype, "totalCaloriesBurned", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Average weekly workout frequency', example: 4.5 }),
    __metadata("design:type", Number)
], MonthlyExerciseStatsDto.prototype, "averageWeeklyFrequency", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Most performed exercises', example: [{ name: 'Push-ups', count: 25 }] }),
    __metadata("design:type", Array)
], MonthlyExerciseStatsDto.prototype, "mostPerformedExercises", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Weekly statistics breakdown', type: [WeeklyExerciseStatsDto] }),
    __metadata("design:type", Array)
], MonthlyExerciseStatsDto.prototype, "weeklyStats", void 0);
class WorkoutPlanProgressDto {
    workoutPlanId;
    planName;
    overallProgress;
    totalDays;
    daysCompleted;
    currentDay;
    startDate;
    expectedCompletionDate;
    dailyProgress;
}
exports.WorkoutPlanProgressDto = WorkoutPlanProgressDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Workout plan ID' }),
    __metadata("design:type", String)
], WorkoutPlanProgressDto.prototype, "workoutPlanId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Workout plan name', example: 'Beginner Strength Training' }),
    __metadata("design:type", String)
], WorkoutPlanProgressDto.prototype, "planName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Overall progress percentage', example: 75.5 }),
    __metadata("design:type", Number)
], WorkoutPlanProgressDto.prototype, "overallProgress", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total days in the plan', example: 30 }),
    __metadata("design:type", Number)
], WorkoutPlanProgressDto.prototype, "totalDays", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Days completed', example: 22 }),
    __metadata("design:type", Number)
], WorkoutPlanProgressDto.prototype, "daysCompleted", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Current day in the plan', example: 23 }),
    __metadata("design:type", Number)
], WorkoutPlanProgressDto.prototype, "currentDay", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Start date of the plan', example: '2024-01-01' }),
    __metadata("design:type", String)
], WorkoutPlanProgressDto.prototype, "startDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Expected completion date', example: '2024-01-30' }),
    __metadata("design:type", String)
], WorkoutPlanProgressDto.prototype, "expectedCompletionDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Daily progress breakdown' }),
    __metadata("design:type", Array)
], WorkoutPlanProgressDto.prototype, "dailyProgress", void 0);
class ExercisePerformanceDto {
    exerciseId;
    exerciseName;
    totalSessions;
    bestWeight;
    bestReps;
    averageWeight;
    averageReps;
    totalCaloriesBurned;
    progressTrend;
    lastPerformed;
}
exports.ExercisePerformanceDto = ExercisePerformanceDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Exercise ID' }),
    __metadata("design:type", String)
], ExercisePerformanceDto.prototype, "exerciseId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Exercise name', example: 'Bench Press' }),
    __metadata("design:type", String)
], ExercisePerformanceDto.prototype, "exerciseName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total times performed', example: 15 }),
    __metadata("design:type", Number)
], ExercisePerformanceDto.prototype, "totalSessions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Best weight lifted', example: 80.5 }),
    __metadata("design:type", Number)
], ExercisePerformanceDto.prototype, "bestWeight", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Best reps in a single set', example: 15 }),
    __metadata("design:type", Number)
], ExercisePerformanceDto.prototype, "bestReps", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Average weight across all sessions', example: 65.5 }),
    __metadata("design:type", Number)
], ExercisePerformanceDto.prototype, "averageWeight", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Average reps per set', example: 10.5 }),
    __metadata("design:type", Number)
], ExercisePerformanceDto.prototype, "averageReps", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total calories burned from this exercise', example: 450.5 }),
    __metadata("design:type", Number)
], ExercisePerformanceDto.prototype, "totalCaloriesBurned", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Progress trend (positive/negative/stable)', example: 'positive' }),
    __metadata("design:type", String)
], ExercisePerformanceDto.prototype, "progressTrend", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Last performed date', example: '2024-01-15' }),
    __metadata("design:type", String)
], ExercisePerformanceDto.prototype, "lastPerformed", void 0);
//# sourceMappingURL=exercise-statistics.dto.js.map