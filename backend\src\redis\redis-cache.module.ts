import { CacheModule } from '@nestjs/cache-manager';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TokenCacheService } from './token-cache.service';
import { createClient } from 'redis';

@Module({
  imports: [
    CacheModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      isGlobal: true,
      useFactory: async (configService: ConfigService) => {
        const client = createClient({
          url: `redis://${configService.get('REDIS_HOST', 'localhost')}:${configService.get('REDIS_PORT', 6379)}`,
          socket: {
            connectTimeout: 10000, // 10 seconds
            reconnectStrategy: (retries) => {
              if (retries > 3) {
                console.error('Redis connection failed after 3 retries');
                return false;
              }
              return Math.min(retries * 50, 500);
            }
          }
        });

        client.on('error', (error) => {
          console.error('Redis Client Error:', error);
        });

        client.on('connect', () => {
          console.log('Connected to Redis');
        });

        client.on('reconnecting', () => {
          console.log('Reconnecting to Redis...');
        });

        try {
          await client.connect();
        } catch (error) {
          console.error('Failed to connect to Redis:', error);
          throw error;
        }

        return {
          store: client,
          ttl: 60 * 60 * 24 * 7, // 7 days default TTL
          isGlobal: true,
        };

      },
    }),
  ],
  providers: [TokenCacheService],
  exports: [CacheModule, TokenCacheService],
})
export class RedisCacheModule {}