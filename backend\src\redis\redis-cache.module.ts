import { CacheModule } from '@nestjs/cache-manager';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TokenCacheService } from './token-cache.service';
import { redisStore } from 'cache-manager-redis-yet';

@Module({
  imports: [
    CacheModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      isGlobal: true,
      useFactory: async (configService: ConfigService) => {
        const store = await redisStore({
          url: `redis://${configService.get('REDIS_HOST', 'localhost')}:${configService.get('REDIS_PORT', 6379)}`,
          socket: {
            connectTimeout: 10000, // 10 seconds
            reconnectStrategy: (retries: number) => {
              if (retries > 3) {
                console.error('Redis connection failed after 3 retries');
                return false;
              }
              return Math.min(retries * 50, 500);
            }
          }
        });

        // Add event listeners to the underlying Redis client
        store.client.on('error', (error: any) => {
          console.error('Redis Client Error:', error);
        });

        store.client.on('connect', () => {
          console.log('Connected to Redis');
          console.log('Redis client info:', {
            isReady: store.client.isReady,
            isOpen: store.client.isOpen,
            options: {
              database: store.client.options?.database,
              url: store.client.options?.url
            }
          });
        });

        store.client.on('reconnecting', () => {
          console.log('Reconnecting to Redis...');
        });

        return {
          store,
          ttl: 60 * 60 * 24 * 7 * 1000, // 7 days in milliseconds
          isGlobal: true,
        };

      },
    }),
  ],
  providers: [TokenCacheService],
  exports: [CacheModule, TokenCacheService],
})
export class RedisCacheModule {}