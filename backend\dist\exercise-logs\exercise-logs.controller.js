"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExerciseLogsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const exercise_logs_service_1 = require("./exercise-logs.service");
const create_exercise_log_dto_1 = require("./dto/create-exercise-log.dto");
const update_exercise_log_dto_1 = require("./dto/update-exercise-log.dto");
const quick_log_exercise_dto_1 = require("./dto/quick-log-exercise.dto");
const exercise_statistics_dto_1 = require("./dto/exercise-statistics.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const roles_decorator_1 = require("../auth/decorators/roles.decorator");
const current_user_decorator_1 = require("../auth/decorators/current-user.decorator");
const client_1 = require("@prisma/client");
let ExerciseLogsController = class ExerciseLogsController {
    exerciseLogsService;
    constructor(exerciseLogsService) {
        this.exerciseLogsService = exerciseLogsService;
    }
    create(createExerciseLogDto) {
        return this.exerciseLogsService.createExerciseLog(createExerciseLogDto);
    }
    quickLog(quickLogDto, user) {
        return this.exerciseLogsService.quickLogExercise(user.id, quickLogDto);
    }
    findUserLogs(userId, startDate, endDate) {
        return this.exerciseLogsService.findUserExerciseLogs(userId, startDate, endDate);
    }
    findMyLogs(user, startDate, endDate) {
        return this.exerciseLogsService.findUserExerciseLogs(user.id, startDate, endDate);
    }
    getDailyStats(userId, date) {
        return this.exerciseLogsService.getDailyStats(userId, date);
    }
    getMyDailyStats(user, date) {
        return this.exerciseLogsService.getDailyStats(user.id, date);
    }
    getWeeklyStats(userId, weekStart) {
        return this.exerciseLogsService.getWeeklyStats(userId, weekStart);
    }
    getMyWeeklyStats(user, weekStart) {
        return this.exerciseLogsService.getWeeklyStats(user.id, weekStart);
    }
    getMonthlyStats(userId, month) {
        return this.exerciseLogsService.getMonthlyStats(userId, month);
    }
    getMyMonthlyStats(user, month) {
        return this.exerciseLogsService.getMonthlyStats(user.id, month);
    }
    getWorkoutPlanProgress(userId, workoutPlanId) {
        return this.exerciseLogsService.getWorkoutPlanProgress(userId, workoutPlanId);
    }
    getMyWorkoutPlanProgress(user, workoutPlanId) {
        return this.exerciseLogsService.getWorkoutPlanProgress(user.id, workoutPlanId);
    }
    getUserWorkoutPlansProgress(userId) {
        return this.exerciseLogsService.getUserWorkoutPlansProgress(userId);
    }
    getMyWorkoutPlansProgress(user) {
        return this.exerciseLogsService.getUserWorkoutPlansProgress(user.id);
    }
    getExercisePerformance(userId, exerciseId) {
        return this.exerciseLogsService.getExercisePerformance(userId, exerciseId);
    }
    getMyExercisePerformance(user, exerciseId) {
        return this.exerciseLogsService.getExercisePerformance(user.id, exerciseId);
    }
    getExerciseStreaks(userId) {
        return this.exerciseLogsService.getExerciseStreaks(userId);
    }
    getMyExerciseStreaks(user) {
        return this.exerciseLogsService.getExerciseStreaks(user.id);
    }
    findOne(id) {
        return this.exerciseLogsService.findExerciseLogById(id);
    }
    update(id, updateExerciseLogDto) {
        return this.exerciseLogsService.updateExerciseLog(id, updateExerciseLogDto);
    }
    remove(id) {
        return this.exerciseLogsService.deleteExerciseLog(id);
    }
};
exports.ExerciseLogsController = ExerciseLogsController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.UserRole.GYMER, client_1.UserRole.COACH),
    (0, swagger_1.ApiOperation)({ summary: 'Log a new exercise session' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Exercise logged successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'User, exercise, or workout plan not found' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_exercise_log_dto_1.CreateExerciseLogDto]),
    __metadata("design:returntype", void 0)
], ExerciseLogsController.prototype, "create", null);
__decorate([
    (0, common_1.Post)('quick-log'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.UserRole.GYMER, client_1.UserRole.COACH),
    (0, swagger_1.ApiOperation)({ summary: 'Quick log an exercise with sets for current user' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Exercise logged successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Exercise not found' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [quick_log_exercise_dto_1.QuickLogExerciseDto, Object]),
    __metadata("design:returntype", void 0)
], ExerciseLogsController.prototype, "quickLog", null);
__decorate([
    (0, common_1.Get)('user/:userId'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.UserRole.GYMER, client_1.UserRole.COACH, client_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Get exercise logs for a user' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'User exercise logs' }),
    (0, swagger_1.ApiQuery)({ name: 'startDate', required: false, description: 'Start date filter (YYYY-MM-DD)' }),
    (0, swagger_1.ApiQuery)({ name: 'endDate', required: false, description: 'End date filter (YYYY-MM-DD)' }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Query)('startDate')),
    __param(2, (0, common_1.Query)('endDate')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", void 0)
], ExerciseLogsController.prototype, "findUserLogs", null);
__decorate([
    (0, common_1.Get)('my-logs'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.UserRole.GYMER, client_1.UserRole.COACH),
    (0, swagger_1.ApiOperation)({ summary: 'Get current user exercise logs' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Current user exercise logs' }),
    (0, swagger_1.ApiQuery)({ name: 'startDate', required: false, description: 'Start date filter (YYYY-MM-DD)' }),
    (0, swagger_1.ApiQuery)({ name: 'endDate', required: false, description: 'End date filter (YYYY-MM-DD)' }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Query)('startDate')),
    __param(2, (0, common_1.Query)('endDate')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String]),
    __metadata("design:returntype", void 0)
], ExerciseLogsController.prototype, "findMyLogs", null);
__decorate([
    (0, common_1.Get)('stats/daily/:userId/:date'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.UserRole.GYMER, client_1.UserRole.COACH, client_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Get daily exercise statistics for a user' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Daily exercise statistics', type: exercise_statistics_dto_1.DailyExerciseStatsDto }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Param)('date')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], ExerciseLogsController.prototype, "getDailyStats", null);
__decorate([
    (0, common_1.Get)('stats/daily/my/:date'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.UserRole.GYMER, client_1.UserRole.COACH),
    (0, swagger_1.ApiOperation)({ summary: 'Get daily exercise statistics for current user' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Daily exercise statistics', type: exercise_statistics_dto_1.DailyExerciseStatsDto }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Param)('date')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], ExerciseLogsController.prototype, "getMyDailyStats", null);
__decorate([
    (0, common_1.Get)('stats/weekly/:userId/:weekStart'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.UserRole.GYMER, client_1.UserRole.COACH, client_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Get weekly exercise statistics for a user' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Weekly exercise statistics', type: exercise_statistics_dto_1.WeeklyExerciseStatsDto }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Param)('weekStart')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], ExerciseLogsController.prototype, "getWeeklyStats", null);
__decorate([
    (0, common_1.Get)('stats/weekly/my/:weekStart'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.UserRole.GYMER, client_1.UserRole.COACH),
    (0, swagger_1.ApiOperation)({ summary: 'Get weekly exercise statistics for current user' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Weekly exercise statistics', type: exercise_statistics_dto_1.WeeklyExerciseStatsDto }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Param)('weekStart')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], ExerciseLogsController.prototype, "getMyWeeklyStats", null);
__decorate([
    (0, common_1.Get)('stats/monthly/:userId/:month'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.UserRole.GYMER, client_1.UserRole.COACH, client_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Get monthly exercise statistics for a user' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Monthly exercise statistics', type: exercise_statistics_dto_1.MonthlyExerciseStatsDto }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Param)('month')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], ExerciseLogsController.prototype, "getMonthlyStats", null);
__decorate([
    (0, common_1.Get)('stats/monthly/my/:month'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.UserRole.GYMER, client_1.UserRole.COACH),
    (0, swagger_1.ApiOperation)({ summary: 'Get monthly exercise statistics for current user' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Monthly exercise statistics', type: exercise_statistics_dto_1.MonthlyExerciseStatsDto }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Param)('month')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], ExerciseLogsController.prototype, "getMyMonthlyStats", null);
__decorate([
    (0, common_1.Get)('workout-plan-progress/:userId/:workoutPlanId'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.UserRole.GYMER, client_1.UserRole.COACH, client_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Get workout plan progress for a user' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Workout plan progress', type: exercise_statistics_dto_1.WorkoutPlanProgressDto }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Param)('workoutPlanId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], ExerciseLogsController.prototype, "getWorkoutPlanProgress", null);
__decorate([
    (0, common_1.Get)('workout-plan-progress/my/:workoutPlanId'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.UserRole.GYMER, client_1.UserRole.COACH),
    (0, swagger_1.ApiOperation)({ summary: 'Get workout plan progress for current user' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Workout plan progress', type: exercise_statistics_dto_1.WorkoutPlanProgressDto }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Param)('workoutPlanId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], ExerciseLogsController.prototype, "getMyWorkoutPlanProgress", null);
__decorate([
    (0, common_1.Get)('workout-plans-progress/:userId'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.UserRole.GYMER, client_1.UserRole.COACH, client_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Get all workout plans progress for a user' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'All workout plans progress', type: [exercise_statistics_dto_1.WorkoutPlanProgressDto] }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ExerciseLogsController.prototype, "getUserWorkoutPlansProgress", null);
__decorate([
    (0, common_1.Get)('workout-plans-progress/my'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.UserRole.GYMER, client_1.UserRole.COACH),
    (0, swagger_1.ApiOperation)({ summary: 'Get all workout plans progress for current user' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'All workout plans progress', type: [exercise_statistics_dto_1.WorkoutPlanProgressDto] }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ExerciseLogsController.prototype, "getMyWorkoutPlansProgress", null);
__decorate([
    (0, common_1.Get)('performance/:userId'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.UserRole.GYMER, client_1.UserRole.COACH, client_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Get exercise performance analytics for a user' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Exercise performance analytics', type: [exercise_statistics_dto_1.ExercisePerformanceDto] }),
    (0, swagger_1.ApiQuery)({ name: 'exerciseId', required: false, description: 'Filter by specific exercise ID' }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Query)('exerciseId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], ExerciseLogsController.prototype, "getExercisePerformance", null);
__decorate([
    (0, common_1.Get)('performance/my'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.UserRole.GYMER, client_1.UserRole.COACH),
    (0, swagger_1.ApiOperation)({ summary: 'Get exercise performance analytics for current user' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Exercise performance analytics', type: [exercise_statistics_dto_1.ExercisePerformanceDto] }),
    (0, swagger_1.ApiQuery)({ name: 'exerciseId', required: false, description: 'Filter by specific exercise ID' }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Query)('exerciseId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], ExerciseLogsController.prototype, "getMyExercisePerformance", null);
__decorate([
    (0, common_1.Get)('streaks/:userId'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.UserRole.GYMER, client_1.UserRole.COACH, client_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Get exercise streaks for a user' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Exercise streaks information' }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ExerciseLogsController.prototype, "getExerciseStreaks", null);
__decorate([
    (0, common_1.Get)('streaks/my'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.UserRole.GYMER, client_1.UserRole.COACH),
    (0, swagger_1.ApiOperation)({ summary: 'Get exercise streaks for current user' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Exercise streaks information' }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], ExerciseLogsController.prototype, "getMyExerciseStreaks", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.UserRole.GYMER, client_1.UserRole.COACH, client_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Get an exercise log by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Exercise log details' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Exercise log not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ExerciseLogsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.UserRole.GYMER, client_1.UserRole.COACH),
    (0, swagger_1.ApiOperation)({ summary: 'Update an exercise log' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Exercise log updated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Exercise log not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_exercise_log_dto_1.UpdateExerciseLogDto]),
    __metadata("design:returntype", void 0)
], ExerciseLogsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.UserRole.GYMER, client_1.UserRole.COACH, client_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Delete an exercise log' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Exercise log deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Exercise log not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ExerciseLogsController.prototype, "remove", null);
exports.ExerciseLogsController = ExerciseLogsController = __decorate([
    (0, swagger_1.ApiTags)('exercise-logs'),
    (0, common_1.Controller)('exercise-logs'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [exercise_logs_service_1.ExerciseLogsService])
], ExerciseLogsController);
//# sourceMappingURL=exercise-logs.controller.js.map