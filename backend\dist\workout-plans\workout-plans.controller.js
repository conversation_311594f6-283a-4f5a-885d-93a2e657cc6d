"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkoutPlansController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const workout_plans_service_1 = require("./workout-plans.service");
const create_workout_plan_dto_1 = require("./dto/create-workout-plan.dto");
const update_workout_plan_dto_1 = require("./dto/update-workout-plan.dto");
const create_workout_exercise_dto_1 = require("./dto/create-workout-exercise.dto");
const create_from_template_dto_1 = require("./dto/create-from-template.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const roles_decorator_1 = require("../auth/decorators/roles.decorator");
const current_user_decorator_1 = require("../auth/decorators/current-user.decorator");
const client_1 = require("@prisma/client");
let WorkoutPlansController = class WorkoutPlansController {
    workoutPlansService;
    constructor(workoutPlansService) {
        this.workoutPlansService = workoutPlansService;
    }
    create(createWorkoutPlanDto, user) {
        return this.workoutPlansService.create(createWorkoutPlanDto, user.id);
    }
    findAll(userId) {
        return this.workoutPlansService.findAll(userId);
    }
    findTemplates(planType, user) {
        return this.workoutPlansService.findTemplates(planType, user?.id);
    }
    findMyTemplates(user) {
        return this.workoutPlansService.findTemplatesByCreator(user.id);
    }
    toggleTemplateStatus(id, body, user) {
        return this.workoutPlansService.toggleTemplateStatus(id, body.isTemplate, user.id);
    }
    findByUserId(userId) {
        return this.workoutPlansService.findByUserId(userId);
    }
    createFromTemplate(templateId, createFromTemplateDto, user) {
        return this.workoutPlansService.createFromTemplate(templateId, createFromTemplateDto, user.id);
    }
    findOne(id) {
        return this.workoutPlansService.findOne(id);
    }
    update(id, updateWorkoutPlanDto, user) {
        return this.workoutPlansService.update(id, updateWorkoutPlanDto, user.id);
    }
    remove(id, user) {
        return this.workoutPlansService.remove(id, user.id);
    }
    addExercise(createWorkoutExerciseDto) {
        return this.workoutPlansService.addExercise(createWorkoutExerciseDto);
    }
    removeExercise(exerciseId) {
        return this.workoutPlansService.removeExercise(exerciseId);
    }
    updateExercise(exerciseId, updateData) {
        return this.workoutPlansService.updateExercise(exerciseId, updateData);
    }
};
exports.WorkoutPlansController = WorkoutPlansController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.UserRole.ADMIN, client_1.UserRole.COACH, client_1.UserRole.GYMER),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new workout plan' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Workout plan created successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_workout_plan_dto_1.CreateWorkoutPlanDto, Object]),
    __metadata("design:returntype", void 0)
], WorkoutPlansController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all workout plans' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'List of all workout plans' }),
    (0, swagger_1.ApiQuery)({ name: 'userId', required: false, description: 'Filter by user ID' }),
    __param(0, (0, common_1.Query)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], WorkoutPlansController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('templates'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all workout plan templates' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'List of all workout plan templates' }),
    (0, swagger_1.ApiQuery)({ name: 'planType', required: false, description: 'Filter templates by plan type' }),
    __param(0, (0, common_1.Query)('planType')),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], WorkoutPlansController.prototype, "findTemplates", null);
__decorate([
    (0, common_1.Get)('templates/my'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.UserRole.ADMIN, client_1.UserRole.COACH),
    (0, swagger_1.ApiOperation)({ summary: 'Get templates created by current user' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'List of templates created by current user' }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], WorkoutPlansController.prototype, "findMyTemplates", null);
__decorate([
    (0, common_1.Patch)(':id/template-status'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.UserRole.ADMIN, client_1.UserRole.COACH),
    (0, swagger_1.ApiOperation)({ summary: 'Toggle template status of a workout plan' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Template status updated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Workout plan not found' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - insufficient permissions' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", void 0)
], WorkoutPlansController.prototype, "toggleTemplateStatus", null);
__decorate([
    (0, common_1.Get)('user/:userId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get workout plans by user ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'User workout plans' }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], WorkoutPlansController.prototype, "findByUserId", null);
__decorate([
    (0, common_1.Post)('clone-template/:templateId'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.UserRole.ADMIN, client_1.UserRole.COACH, client_1.UserRole.GYMER),
    (0, swagger_1.ApiOperation)({ summary: 'Create a workout plan from a template' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Workout plan created from template successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Template not found' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    __param(0, (0, common_1.Param)('templateId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, create_from_template_dto_1.CreateFromTemplateDto, Object]),
    __metadata("design:returntype", void 0)
], WorkoutPlansController.prototype, "createFromTemplate", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a workout plan by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Workout plan details' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Workout plan not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], WorkoutPlansController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.UserRole.ADMIN, client_1.UserRole.COACH, client_1.UserRole.GYMER),
    (0, swagger_1.ApiOperation)({ summary: 'Update a workout plan' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Workout plan updated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Workout plan not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_workout_plan_dto_1.UpdateWorkoutPlanDto, Object]),
    __metadata("design:returntype", void 0)
], WorkoutPlansController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.UserRole.ADMIN, client_1.UserRole.COACH, client_1.UserRole.GYMER),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a workout plan' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Workout plan deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Workout plan not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], WorkoutPlansController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)('exercises'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.UserRole.ADMIN, client_1.UserRole.COACH, client_1.UserRole.GYMER),
    (0, swagger_1.ApiOperation)({ summary: 'Add exercise to workout plan' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Exercise added to workout plan successfully' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_workout_exercise_dto_1.CreateWorkoutExerciseDto]),
    __metadata("design:returntype", void 0)
], WorkoutPlansController.prototype, "addExercise", null);
__decorate([
    (0, common_1.Delete)('exercises/:exerciseId'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.UserRole.ADMIN, client_1.UserRole.COACH, client_1.UserRole.GYMER),
    (0, swagger_1.ApiOperation)({ summary: 'Remove exercise from workout plan' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Exercise removed from workout plan successfully' }),
    __param(0, (0, common_1.Param)('exerciseId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], WorkoutPlansController.prototype, "removeExercise", null);
__decorate([
    (0, common_1.Patch)('exercises/:exerciseId'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.UserRole.ADMIN, client_1.UserRole.COACH, client_1.UserRole.GYMER),
    (0, swagger_1.ApiOperation)({ summary: 'Update exercise in workout plan' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Exercise updated successfully' }),
    __param(0, (0, common_1.Param)('exerciseId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], WorkoutPlansController.prototype, "updateExercise", null);
exports.WorkoutPlansController = WorkoutPlansController = __decorate([
    (0, swagger_1.ApiTags)('workout-plans'),
    (0, common_1.Controller)('workout-plans'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [workout_plans_service_1.WorkoutPlansService])
], WorkoutPlansController);
//# sourceMappingURL=workout-plans.controller.js.map