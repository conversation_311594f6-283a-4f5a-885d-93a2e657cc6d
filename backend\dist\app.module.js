"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
const prisma_module_1 = require("./prisma/prisma.module");
const auth_module_1 = require("./auth/auth.module");
const coaches_module_1 = require("./coaches/coaches.module");
const gymers_module_1 = require("./gymers/gymers.module");
const workout_plans_module_1 = require("./workout-plans/workout-plans.module");
const exercises_module_1 = require("./exercises/exercises.module");
const appointments_module_1 = require("./appointments/appointments.module");
const training_requests_module_1 = require("./training-requests/training-requests.module");
const feedbacks_module_1 = require("./feedbacks/feedbacks.module");
const muscle_groups_module_1 = require("./muscle-groups/muscle-groups.module");
const equipment_module_1 = require("./equipment/equipment.module");
const email_module_1 = require("./email/email.module");
const user_profile_module_1 = require("./user-profile/user-profile.module");
const exercise_logs_module_1 = require("./exercise-logs/exercise-logs.module");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                envFilePath: '.env',
            }),
            prisma_module_1.PrismaModule,
            auth_module_1.AuthModule,
            coaches_module_1.CoachesModule,
            gymers_module_1.GymersModule,
            workout_plans_module_1.WorkoutPlansModule,
            exercises_module_1.ExercisesModule,
            appointments_module_1.AppointmentsModule,
            training_requests_module_1.TrainingRequestsModule,
            feedbacks_module_1.FeedbacksModule,
            muscle_groups_module_1.MuscleGroupsModule,
            equipment_module_1.EquipmentModule,
            email_module_1.EmailModule,
            user_profile_module_1.UserProfileModule,
            exercise_logs_module_1.ExerciseLogsModule,
        ],
        controllers: [app_controller_1.AppController],
        providers: [app_service_1.AppService],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map