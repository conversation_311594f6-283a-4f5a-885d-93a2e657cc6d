import { ExerciseLogsService } from './exercise-logs.service';
import { CreateExerciseLogDto } from './dto/create-exercise-log.dto';
import { UpdateExerciseLogDto } from './dto/update-exercise-log.dto';
import { QuickLogExerciseDto } from './dto/quick-log-exercise.dto';
import { DailyExerciseStatsDto, WeeklyExerciseStatsDto, MonthlyExerciseStatsDto, WorkoutPlanProgressDto, ExercisePerformanceDto } from './dto/exercise-statistics.dto';
export declare class ExerciseLogsController {
    private readonly exerciseLogsService;
    constructor(exerciseLogsService: ExerciseLogsService);
    create(createExerciseLogDto: CreateExerciseLogDto): Promise<{
        setsLog: {
            id: string;
            weight: number | null;
            setNumber: number | null;
            reps: number | null;
            times: number | null;
            caloriesBurned: number | null;
            exerciseLogId: string;
        }[];
    } & {
        id: string;
        workoutLogId: string;
    }>;
    quickLog(quickLogDto: QuickLogExerciseDto, user: any): Promise<{
        setsLog: {
            id: string;
            weight: number | null;
            setNumber: number | null;
            reps: number | null;
            times: number | null;
            caloriesBurned: number | null;
            exerciseLogId: string;
        }[];
    } & {
        id: string;
        workoutLogId: string;
    }>;
    findUserLogs(userId: string, startDate?: string, endDate?: string): Promise<({
        workoutExerciseLogs: ({
            workoutExercise: {
                workoutPlan: {
                    name: string | null;
                    id: string;
                    planType: import(".prisma/client").$Enums.PlanType | null;
                };
            } & {
                id: string;
                weight: number | null;
                workoutPlanId: string;
                exerciseId: string | null;
                dayNumber: number | null;
            };
        } & {
            id: string;
            dayNumber: number | null;
            caloriesBurned: number | null;
            date: Date | null;
            progressPercent: number | null;
            logId: string;
            workoutExerciseId: string;
        })[];
    } & {
        id: string;
        weight: number | null;
        height: number | null;
        userId: string;
        caloriesBurned: number | null;
        notes: string | null;
        dateLogged: Date | null;
        caloriesIntake: number | null;
        mealId: string | null;
    })[]>;
    findMyLogs(user: any, startDate?: string, endDate?: string): Promise<({
        workoutExerciseLogs: ({
            workoutExercise: {
                workoutPlan: {
                    name: string | null;
                    id: string;
                    planType: import(".prisma/client").$Enums.PlanType | null;
                };
            } & {
                id: string;
                weight: number | null;
                workoutPlanId: string;
                exerciseId: string | null;
                dayNumber: number | null;
            };
        } & {
            id: string;
            dayNumber: number | null;
            caloriesBurned: number | null;
            date: Date | null;
            progressPercent: number | null;
            logId: string;
            workoutExerciseId: string;
        })[];
    } & {
        id: string;
        weight: number | null;
        height: number | null;
        userId: string;
        caloriesBurned: number | null;
        notes: string | null;
        dateLogged: Date | null;
        caloriesIntake: number | null;
        mealId: string | null;
    })[]>;
    getDailyStats(userId: string, date: string): Promise<DailyExerciseStatsDto>;
    getMyDailyStats(user: any, date: string): Promise<DailyExerciseStatsDto>;
    getWeeklyStats(userId: string, weekStart: string): Promise<WeeklyExerciseStatsDto>;
    getMyWeeklyStats(user: any, weekStart: string): Promise<WeeklyExerciseStatsDto>;
    getMonthlyStats(userId: string, month: string): Promise<MonthlyExerciseStatsDto>;
    getMyMonthlyStats(user: any, month: string): Promise<MonthlyExerciseStatsDto>;
    getWorkoutPlanProgress(userId: string, workoutPlanId: string): Promise<WorkoutPlanProgressDto>;
    getMyWorkoutPlanProgress(user: any, workoutPlanId: string): Promise<WorkoutPlanProgressDto>;
    getUserWorkoutPlansProgress(userId: string): Promise<WorkoutPlanProgressDto[]>;
    getMyWorkoutPlansProgress(user: any): Promise<WorkoutPlanProgressDto[]>;
    getExercisePerformance(userId: string, exerciseId?: string): Promise<ExercisePerformanceDto[]>;
    getMyExercisePerformance(user: any, exerciseId?: string): Promise<ExercisePerformanceDto[]>;
    getExerciseStreaks(userId: string): Promise<{
        currentStreak: number;
        longestStreak: number;
        lastWorkoutDate: string | null;
    }>;
    getMyExerciseStreaks(user: any): Promise<{
        currentStreak: number;
        longestStreak: number;
        lastWorkoutDate: string | null;
    }>;
    findOne(id: string): Promise<{
        setsLog: {
            id: string;
            weight: number | null;
            setNumber: number | null;
            reps: number | null;
            times: number | null;
            caloriesBurned: number | null;
            exerciseLogId: string;
        }[];
    } & {
        id: string;
        workoutLogId: string;
    }>;
    update(id: string, updateExerciseLogDto: UpdateExerciseLogDto): Promise<{
        setsLog: {
            id: string;
            weight: number | null;
            setNumber: number | null;
            reps: number | null;
            times: number | null;
            caloriesBurned: number | null;
            exerciseLogId: string;
        }[];
    } & {
        id: string;
        workoutLogId: string;
    }>;
    remove(id: string): Promise<{
        id: string;
        workoutLogId: string;
    }>;
}
