"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExercisesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const exercises_service_1 = require("./exercises.service");
const create_exercise_dto_1 = require("./dto/create-exercise.dto");
const update_exercise_dto_1 = require("./dto/update-exercise.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const roles_decorator_1 = require("../auth/decorators/roles.decorator");
const current_user_decorator_1 = require("../auth/decorators/current-user.decorator");
const client_1 = require("@prisma/client");
const exercise_logs_service_1 = require("../exercise-logs/exercise-logs.service");
let ExercisesController = class ExercisesController {
    exercisesService;
    exerciseLogsService;
    constructor(exercisesService, exerciseLogsService) {
        this.exercisesService = exercisesService;
        this.exerciseLogsService = exerciseLogsService;
    }
    create(createExerciseDto) {
        return this.exercisesService.create(createExerciseDto);
    }
    findAll(userId, search) {
        if (search) {
            return this.exercisesService.search(search);
        }
        return this.exercisesService.findAll(userId);
    }
    findByMuscleGroup(muscleGroupId) {
        return this.exercisesService.findByMuscleGroup(muscleGroupId);
    }
    findOne(id) {
        return this.exercisesService.findOne(id);
    }
    update(id, updateExerciseDto) {
        return this.exercisesService.update(id, updateExerciseDto);
    }
    remove(id) {
        return this.exercisesService.remove(id);
    }
    getExercisePerformance(exerciseId, userId) {
        return this.exerciseLogsService.getExercisePerformance(userId, exerciseId);
    }
    getMyExercisePerformance(exerciseId, user) {
        return this.exerciseLogsService.getExercisePerformance(user.id, exerciseId);
    }
};
exports.ExercisesController = ExercisesController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.UserRole.ADMIN, client_1.UserRole.COACH),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new exercise' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Exercise created successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_exercise_dto_1.CreateExerciseDto]),
    __metadata("design:returntype", void 0)
], ExercisesController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all exercises' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'List of all exercises' }),
    (0, swagger_1.ApiQuery)({ name: 'userId', required: false, description: 'Filter by user ID' }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, description: 'Search exercises' }),
    __param(0, (0, common_1.Query)('userId')),
    __param(1, (0, common_1.Query)('search')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", void 0)
], ExercisesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('muscle-group/:muscleGroupId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get exercises by muscle group' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Exercises for muscle group' }),
    __param(0, (0, common_1.Param)('muscleGroupId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ExercisesController.prototype, "findByMuscleGroup", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get an exercise by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Exercise details' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Exercise not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ExercisesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.UserRole.ADMIN, client_1.UserRole.COACH),
    (0, swagger_1.ApiOperation)({ summary: 'Update an exercise' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Exercise updated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Exercise not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_exercise_dto_1.UpdateExerciseDto]),
    __metadata("design:returntype", void 0)
], ExercisesController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.UserRole.ADMIN, client_1.UserRole.COACH),
    (0, swagger_1.ApiOperation)({ summary: 'Delete an exercise' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Exercise deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Exercise not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ExercisesController.prototype, "remove", null);
__decorate([
    (0, common_1.Get)(':id/performance/:userId'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.UserRole.GYMER, client_1.UserRole.COACH, client_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Get performance analytics for a specific exercise and user' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Exercise performance analytics' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Param)('userId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", void 0)
], ExercisesController.prototype, "getExercisePerformance", null);
__decorate([
    (0, common_1.Get)(':id/performance/my'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.UserRole.GYMER, client_1.UserRole.COACH),
    (0, swagger_1.ApiOperation)({ summary: 'Get performance analytics for a specific exercise for current user' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Exercise performance analytics' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], ExercisesController.prototype, "getMyExercisePerformance", null);
exports.ExercisesController = ExercisesController = __decorate([
    (0, swagger_1.ApiTags)('exercises'),
    (0, common_1.Controller)('exercises'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [exercises_service_1.ExercisesService,
        exercise_logs_service_1.ExerciseLogsService])
], ExercisesController);
//# sourceMappingURL=exercises.controller.js.map