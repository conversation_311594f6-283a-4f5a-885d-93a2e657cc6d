"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RedisCacheModule = void 0;
const cache_manager_1 = require("@nestjs/cache-manager");
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const token_cache_service_1 = require("./token-cache.service");
const redis_1 = require("redis");
let RedisCacheModule = class RedisCacheModule {
};
exports.RedisCacheModule = RedisCacheModule;
exports.RedisCacheModule = RedisCacheModule = __decorate([
    (0, common_1.Module)({
        imports: [
            cache_manager_1.CacheModule.registerAsync({
                imports: [config_1.ConfigModule],
                inject: [config_1.ConfigService],
                isGlobal: true,
                useFactory: async (configService) => {
                    const client = (0, redis_1.createClient)({
                        url: `redis://${configService.get('REDIS_HOST', 'localhost')}:${configService.get('REDIS_PORT', 6379)}`,
                        socket: {
                            connectTimeout: 10000,
                            reconnectStrategy: (retries) => {
                                if (retries > 3) {
                                    console.error('Redis connection failed after 3 retries');
                                    return false;
                                }
                                return Math.min(retries * 50, 500);
                            }
                        }
                    });
                    client.on('error', (error) => {
                        console.error('Redis Client Error:', error);
                    });
                    client.on('connect', () => {
                        console.log('Connected to Redis');
                    });
                    client.on('reconnecting', () => {
                        console.log('Reconnecting to Redis...');
                    });
                    try {
                        await client.connect();
                    }
                    catch (error) {
                        console.error('Failed to connect to Redis:', error);
                        throw error;
                    }
                    return {
                        store: client,
                        ttl: 60 * 60 * 24 * 7,
                        isGlobal: true,
                    };
                },
            }),
        ],
        providers: [token_cache_service_1.TokenCacheService],
        exports: [cache_manager_1.CacheModule, token_cache_service_1.TokenCacheService],
    })
], RedisCacheModule);
//# sourceMappingURL=redis-cache.module.js.map