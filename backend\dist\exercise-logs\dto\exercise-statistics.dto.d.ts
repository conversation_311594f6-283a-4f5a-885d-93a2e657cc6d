export declare class DailyExerciseStatsDto {
    date: string;
    totalExercises: number;
    totalSets: number;
    totalReps: number;
    totalCaloriesBurned: number;
    totalWorkoutTime: number;
    averageWeight: number;
    workoutPlansCompleted: string[];
}
export declare class WeeklyExerciseStatsDto {
    weekStart: string;
    weekEnd: string;
    totalWorkoutDays: number;
    totalExercises: number;
    totalCaloriesBurned: number;
    averageDailyWorkoutTime: number;
    dailyStats: DailyExerciseStatsDto[];
}
export declare class MonthlyExerciseStatsDto {
    month: string;
    totalWorkoutDays: number;
    totalExercises: number;
    totalCaloriesBurned: number;
    averageWeeklyFrequency: number;
    mostPerformedExercises: {
        name: string;
        count: number;
    }[];
    weeklyStats: WeeklyExerciseStatsDto[];
}
export declare class WorkoutPlanProgressDto {
    workoutPlanId: string;
    planName: string;
    overallProgress: number;
    totalDays: number;
    daysCompleted: number;
    currentDay: number;
    startDate: string;
    expectedCompletionDate: string;
    dailyProgress: {
        dayNumber: number;
        date: string;
        completed: boolean;
        exercisesCompleted: number;
        totalExercises: number;
        progressPercent: number;
    }[];
}
export declare class ExercisePerformanceDto {
    exerciseId: string;
    exerciseName: string;
    totalSessions: number;
    bestWeight: number;
    bestReps: number;
    averageWeight: number;
    averageReps: number;
    totalCaloriesBurned: number;
    progressTrend: string;
    lastPerformed: string;
}
