import { ExercisesService } from './exercises.service';
import { CreateExerciseDto } from './dto/create-exercise.dto';
import { UpdateExerciseDto } from './dto/update-exercise.dto';
import { ExerciseLogsService } from '../exercise-logs/exercise-logs.service';
export declare class ExercisesController {
    private readonly exercisesService;
    private readonly exerciseLogsService;
    constructor(exercisesService: ExercisesService, exerciseLogsService: ExerciseLogsService);
    create(createExerciseDto: CreateExerciseDto): Promise<{
        description: string | null;
        name: string | null;
        id: string;
        userId: string | null;
        equipmentId: string | null;
        instruction: string | null;
        videoUrl: string | null;
        met: number | null;
        defaultWeight: number | null;
        defaultSets: number | null;
        defaultReps: number | null;
        restTime: number | null;
    }>;
    findAll(userId?: string, search?: string): Promise<{
        description: string | null;
        name: string | null;
        id: string;
        userId: string | null;
        equipmentId: string | null;
        instruction: string | null;
        videoUrl: string | null;
        met: number | null;
        defaultWeight: number | null;
        defaultSets: number | null;
        defaultReps: number | null;
        restTime: number | null;
    }[]>;
    findByMuscleGroup(muscleGroupId: string): Promise<{
        description: string | null;
        name: string | null;
        id: string;
        userId: string | null;
        equipmentId: string | null;
        instruction: string | null;
        videoUrl: string | null;
        met: number | null;
        defaultWeight: number | null;
        defaultSets: number | null;
        defaultReps: number | null;
        restTime: number | null;
    }[]>;
    findOne(id: string): Promise<{
        description: string | null;
        name: string | null;
        id: string;
        userId: string | null;
        equipmentId: string | null;
        instruction: string | null;
        videoUrl: string | null;
        met: number | null;
        defaultWeight: number | null;
        defaultSets: number | null;
        defaultReps: number | null;
        restTime: number | null;
    }>;
    update(id: string, updateExerciseDto: UpdateExerciseDto): Promise<{
        description: string | null;
        name: string | null;
        id: string;
        userId: string | null;
        equipmentId: string | null;
        instruction: string | null;
        videoUrl: string | null;
        met: number | null;
        defaultWeight: number | null;
        defaultSets: number | null;
        defaultReps: number | null;
        restTime: number | null;
    }>;
    remove(id: string): Promise<{
        description: string | null;
        name: string | null;
        id: string;
        userId: string | null;
        equipmentId: string | null;
        instruction: string | null;
        videoUrl: string | null;
        met: number | null;
        defaultWeight: number | null;
        defaultSets: number | null;
        defaultReps: number | null;
        restTime: number | null;
    }>;
    getExercisePerformance(exerciseId: string, userId: string): Promise<import("../exercise-logs/dto/exercise-statistics.dto").ExercisePerformanceDto[]>;
    getMyExercisePerformance(exerciseId: string, user: any): Promise<import("../exercise-logs/dto/exercise-statistics.dto").ExercisePerformanceDto[]>;
}
