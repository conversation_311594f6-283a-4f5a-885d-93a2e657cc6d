import { Cache } from 'cache-manager';
export declare class TokenCacheService {
    private cacheManager;
    private readonly logger;
    constructor(cacheManager: Cache);
    storeRefreshToken(userId: string, refreshToken: string, ttl?: number): Promise<void>;
    getUserIdByRefreshToken(refreshToken: string): Promise<string | null>;
    removeRefreshToken(refreshToken: string): Promise<void>;
    removeAllUserRefreshTokens(userId: string): Promise<void>;
}
