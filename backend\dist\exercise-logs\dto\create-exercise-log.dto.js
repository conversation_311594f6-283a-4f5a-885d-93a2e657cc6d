"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateExerciseLogDto = exports.CreateSetLogDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class CreateSetLogDto {
    setNumber;
    reps;
    times;
    weight;
    caloriesBurned;
}
exports.CreateSetLogDto = CreateSetLogDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Set number in the exercise', example: 1 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], CreateSetLogDto.prototype, "setNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Number of repetitions', example: 12 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateSetLogDto.prototype, "reps", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Time duration in seconds', example: 60 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateSetLogDto.prototype, "times", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Weight used in kg', example: 50.5 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateSetLogDto.prototype, "weight", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Calories burned in this set', example: 15.5 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateSetLogDto.prototype, "caloriesBurned", void 0);
class CreateExerciseLogDto {
    userId;
    exerciseId;
    workoutPlanId;
    dayNumber;
    date;
    exerciseName;
    totalCaloriesBurned;
    progressPercent;
    notes;
    sets;
}
exports.CreateExerciseLogDto = CreateExerciseLogDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User ID who performed the exercise' }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreateExerciseLogDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Exercise ID from the exercise library', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreateExerciseLogDto.prototype, "exerciseId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Workout plan ID if part of a plan', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreateExerciseLogDto.prototype, "workoutPlanId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Day number in the workout plan', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], CreateExerciseLogDto.prototype, "dayNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Date when the exercise was performed', example: '2024-01-15' }),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CreateExerciseLogDto.prototype, "date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Exercise name if custom exercise' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateExerciseLogDto.prototype, "exerciseName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total calories burned in the exercise', example: 150.5 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateExerciseLogDto.prototype, "totalCaloriesBurned", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Progress percentage for the workout plan', example: 75.5 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], CreateExerciseLogDto.prototype, "progressPercent", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Additional notes about the exercise session' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateExerciseLogDto.prototype, "notes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Array of sets performed in this exercise', type: [CreateSetLogDto] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => CreateSetLogDto),
    __metadata("design:type", Array)
], CreateExerciseLogDto.prototype, "sets", void 0);
//# sourceMappingURL=create-exercise-log.dto.js.map