import { Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { AuthService } from '../auth.service';
import { UserRole } from '@prisma/client';
export interface JwtPayload {
    sub: string;
    email: string | null;
    role: UserRole | null;
}
declare const JwtStrategy_base: new (...args: [opt: import("passport-jwt").StrategyOptionsWithRequest] | [opt: import("passport-jwt").StrategyOptionsWithoutRequest]) => Strategy & {
    validate(...args: any[]): unknown;
};
export declare class JwtStrategy extends JwtStrategy_base {
    private configService;
    private authService;
    constructor(configService: ConfigService, authService: AuthService);
    validate(payload: JwtPayload): Promise<{
        name: string | null;
        id: string;
        email: string | null;
        username: string | null;
        password: string | null;
        profilePicture: string | null;
        dateOfBirth: Date | null;
        address: string | null;
        role: import(".prisma/client").$Enums.UserRole | null;
        phoneNumber: string | null;
        premiumStatus: boolean | null;
        weight: number | null;
        height: number | null;
        biography: string | null;
        goal: import(".prisma/client").$Enums.FitnessGoal | null;
        oneRm: number | null;
        expType: string | null;
        sex: import(".prisma/client").$Enums.Gender | null;
        isEmailVerified: boolean | null;
        emailVerificationOTP: string | null;
        emailVerificationOTPExpires: Date | null;
        passwordResetOTP: string | null;
        passwordResetOTPExpires: Date | null;
        googleId: string | null;
        createdAt: Date;
        updatedAt: Date;
    }>;
}
export {};
