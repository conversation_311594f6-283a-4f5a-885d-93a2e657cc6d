"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuickLogExerciseDto = exports.QuickSetDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class QuickSetDto {
    reps;
    weight;
    duration;
}
exports.QuickSetDto = QuickSetDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Number of repetitions', example: 12 }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], QuickSetDto.prototype, "reps", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Weight used in kg', example: 50.5 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], QuickSetDto.prototype, "weight", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Time duration in seconds', example: 60 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], QuickSetDto.prototype, "duration", void 0);
class QuickLogExerciseDto {
    exerciseId;
    workoutPlanId;
    dayNumber;
    sets;
    notes;
}
exports.QuickLogExerciseDto = QuickLogExerciseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Exercise ID from the exercise library' }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], QuickLogExerciseDto.prototype, "exerciseId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Workout plan ID if part of a plan', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], QuickLogExerciseDto.prototype, "workoutPlanId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Day number in the workout plan', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], QuickLogExerciseDto.prototype, "dayNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Array of sets performed', type: [QuickSetDto] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => QuickSetDto),
    __metadata("design:type", Array)
], QuickLogExerciseDto.prototype, "sets", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Additional notes', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], QuickLogExerciseDto.prototype, "notes", void 0);
//# sourceMappingURL=quick-log-exercise.dto.js.map